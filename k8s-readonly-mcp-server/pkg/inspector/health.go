package inspector

import (
	"context"
	"fmt"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// HealthReport 提供集群健康状况的综合报告
type HealthReport struct {
	OverallHealth   bool          `json:"overall_health"`
	Status          string        `json:"status"`
	NodeHealth      NodeHealth    `json:"node_health"`
	SystemHealth    SystemHealth  `json:"system_health"`
	PodHealth       PodHealth     `json:"pod_health"`
	Issues          []HealthIssue `json:"issues"`
	Recommendations []string      `json:"recommendations"`
	Timestamp       time.Time     `json:"timestamp"`
}

// NodeHealth 节点健康状况
type NodeHealth struct {
	TotalNodes     int `json:"total_nodes"`
	ReadyNodes     int `json:"ready_nodes"`
	UnreadyNodes   int `json:"unready_nodes"`
	MemoryPressure int `json:"memory_pressure"`
	DiskPressure   int `json:"disk_pressure"`
}

// SystemHealth 系统组件健康状况
type SystemHealth struct {
	TotalSystemPods   int `json:"total_system_pods"`
	RunningSystemPods int `json:"running_system_pods"`
	FailedSystemPods  int `json:"failed_system_pods"`
	PendingSystemPods int `json:"pending_system_pods"`
}

// PodHealth Pod整体健康状况
type PodHealth struct {
	TotalPods   int            `json:"total_pods"`
	PodsByPhase map[string]int `json:"pods_by_phase"`
}

// HealthIssue 健康问题详情
type HealthIssue struct {
	Type     string `json:"type"`
	Resource string `json:"resource"`
	Message  string `json:"message"`
	Severity string `json:"severity"` // critical, warning, info
}

// GetHealthReport 获取集群健康状况报告
func (i *Inspector) GetHealthReport(ctx context.Context) (*HealthReport, error) {
	report := &HealthReport{
		Timestamp:       time.Now(),
		Issues:          []HealthIssue{},
		Recommendations: []string{},
	}

	// 获取基础数据
	nodes, err := i.clientset.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list nodes: %w", err)
	}

	allPods, err := i.clientset.CoreV1().Pods("").List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list pods: %w", err)
	}

	systemPods, err := i.clientset.CoreV1().Pods("kube-system").List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list system pods: %w", err)
	}

	// 分析节点健康
	report.NodeHealth = analyzeNodeHealth(nodes.Items, report)

	// 分析系统组件健康
	report.SystemHealth = analyzeSystemHealth(systemPods.Items, report)

	// 分析Pod整体健康
	report.PodHealth = analyzePodHealth(allPods.Items)

	// 确定总体健康状态
	report.OverallHealth = report.NodeHealth.ReadyNodes == report.NodeHealth.TotalNodes &&
		report.SystemHealth.FailedSystemPods == 0

	if report.OverallHealth {
		report.Status = "healthy"
	} else {
		report.Status = "degraded"
	}

	// 生成建议
	report.generateRecommendations()

	return report, nil
}

// analyzeNodeHealth 分析节点健康状况
func analyzeNodeHealth(nodes []corev1.Node, report *HealthReport) NodeHealth {
	health := NodeHealth{
		TotalNodes: len(nodes),
	}

	for _, node := range nodes {
		isReady := true
		for _, condition := range node.Status.Conditions {
			switch condition.Type {
			case corev1.NodeReady:
				if condition.Status != corev1.ConditionTrue {
					isReady = false
					health.UnreadyNodes++
					report.Issues = append(report.Issues, HealthIssue{
						Type:     "node",
						Resource: node.Name,
						Message:  fmt.Sprintf("Node not ready: %s", condition.Message),
						Severity: "critical",
					})
				}
			case corev1.NodeMemoryPressure:
				if condition.Status == corev1.ConditionTrue {
					health.MemoryPressure++
					report.Issues = append(report.Issues, HealthIssue{
						Type:     "node",
						Resource: node.Name,
						Message:  "Memory pressure detected",
						Severity: "warning",
					})
				}
			case corev1.NodeDiskPressure:
				if condition.Status == corev1.ConditionTrue {
					health.DiskPressure++
					report.Issues = append(report.Issues, HealthIssue{
						Type:     "node",
						Resource: node.Name,
						Message:  "Disk pressure detected",
						Severity: "warning",
					})
				}
			}
		}
		if isReady {
			health.ReadyNodes++
		}
	}

	return health
}

// analyzeSystemHealth 分析系统组件健康状况
func analyzeSystemHealth(systemPods []corev1.Pod, report *HealthReport) SystemHealth {
	health := SystemHealth{
		TotalSystemPods: len(systemPods),
	}

	for _, pod := range systemPods {
		switch pod.Status.Phase {
		case corev1.PodRunning:
			health.RunningSystemPods++
		case corev1.PodFailed:
			health.FailedSystemPods++
			report.Issues = append(report.Issues, HealthIssue{
				Type:     "system_pod",
				Resource: fmt.Sprintf("%s/%s", pod.Namespace, pod.Name),
				Message:  fmt.Sprintf("System pod failed: %s", pod.Status.Message),
				Severity: "critical",
			})
		case corev1.PodPending:
			health.PendingSystemPods++
			report.Issues = append(report.Issues, HealthIssue{
				Type:     "system_pod",
				Resource: fmt.Sprintf("%s/%s", pod.Namespace, pod.Name),
				Message:  "System pod stuck in pending state",
				Severity: "warning",
			})
		}
	}

	return health
}

// analyzePodHealth 分析Pod整体健康状况
func analyzePodHealth(allPods []corev1.Pod) PodHealth {
	health := PodHealth{
		TotalPods:   len(allPods),
		PodsByPhase: make(map[string]int),
	}

	for _, pod := range allPods {
		health.PodsByPhase[string(pod.Status.Phase)]++
	}

	return health
}

// generateRecommendations 生成健康建议
func (h *HealthReport) generateRecommendations() {
	if h.NodeHealth.UnreadyNodes > 0 {
		h.Recommendations = append(h.Recommendations,
			fmt.Sprintf("Investigate %d unready nodes", h.NodeHealth.UnreadyNodes))
	}

	if h.NodeHealth.MemoryPressure > 0 {
		h.Recommendations = append(h.Recommendations,
			"Consider adding memory or optimizing workloads to reduce memory pressure")
	}

	if h.NodeHealth.DiskPressure > 0 {
		h.Recommendations = append(h.Recommendations,
			"Free up disk space or add storage capacity")
	}

	if h.SystemHealth.FailedSystemPods > 0 {
		h.Recommendations = append(h.Recommendations,
			"Check failed system pods in kube-system namespace")
	}

	if h.SystemHealth.PendingSystemPods > 0 {
		h.Recommendations = append(h.Recommendations,
			"Review pending system pods for scheduling issues")
	}

	if len(h.Issues) == 0 {
		h.Recommendations = append(h.Recommendations,
			"Cluster appears healthy - continue monitoring")
	}
}

// FormatHealthReport 格式化健康报告为易读文本
func (h *HealthReport) FormatHealthReport() string {
	var sb strings.Builder

	sb.WriteString(fmt.Sprintf("Cluster Health Report - %s\n", h.Timestamp.Format("2006-01-02 15:04:05")))
	sb.WriteString(fmt.Sprintf("Overall Status: %s\n\n", h.Status))

	sb.WriteString("Node Health:\n")
	sb.WriteString(fmt.Sprintf("  Total Nodes: %d\n", h.NodeHealth.TotalNodes))
	sb.WriteString(fmt.Sprintf("  Ready Nodes: %d\n", h.NodeHealth.ReadyNodes))
	sb.WriteString(fmt.Sprintf("  Unready Nodes: %d\n", h.NodeHealth.UnreadyNodes))
	if h.NodeHealth.MemoryPressure > 0 {
		sb.WriteString(fmt.Sprintf("  Memory Pressure: %d nodes\n", h.NodeHealth.MemoryPressure))
	}
	if h.NodeHealth.DiskPressure > 0 {
		sb.WriteString(fmt.Sprintf("  Disk Pressure: %d nodes\n", h.NodeHealth.DiskPressure))
	}

	sb.WriteString("\nSystem Component Health:\n")
	sb.WriteString(fmt.Sprintf("  Total System Pods: %d\n", h.SystemHealth.TotalSystemPods))
	sb.WriteString(fmt.Sprintf("  Running: %d\n", h.SystemHealth.RunningSystemPods))
	if h.SystemHealth.FailedSystemPods > 0 {
		sb.WriteString(fmt.Sprintf("  Failed: %d\n", h.SystemHealth.FailedSystemPods))
	}
	if h.SystemHealth.PendingSystemPods > 0 {
		sb.WriteString(fmt.Sprintf("  Pending: %d\n", h.SystemHealth.PendingSystemPods))
	}

	sb.WriteString("\nPod Overview:\n")
	sb.WriteString(fmt.Sprintf("  Total Pods: %d\n", h.PodHealth.TotalPods))
	for phase, count := range h.PodHealth.PodsByPhase {
		sb.WriteString(fmt.Sprintf("  %s: %d\n", phase, count))
	}

	if len(h.Issues) > 0 {
		sb.WriteString("\nIssues Detected:\n")
		for _, issue := range h.Issues {
			sb.WriteString(fmt.Sprintf("  [%s] %s: %s\n", issue.Severity, issue.Resource, issue.Message))
		}
	}

	if len(h.Recommendations) > 0 {
		sb.WriteString("\nRecommendations:\n")
		for _, rec := range h.Recommendations {
			sb.WriteString(fmt.Sprintf("  • %s\n", rec))
		}
	}

	return sb.String()
}
