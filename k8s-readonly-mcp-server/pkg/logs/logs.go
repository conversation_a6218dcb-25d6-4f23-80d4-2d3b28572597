package logs

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

// LogOptions 定义获取日志的各种选项
type LogOptions struct {
	Container  string     // 容器名称（对于多容器Pod）
	Follow     bool       // 是否跟随日志流
	Previous   bool       // 是否获取前一个容器的日志
	TailLines  *int64     // 尾部行数
	SinceTime  *time.Time // 起始时间
	LimitBytes *int64     // 限制字节数
}

// ContainerInfo 包含容器的基本信息
type ContainerInfo struct {
	Name  string
	Image string
	State string
	Ready bool
}

// PodLogInfo 包含Pod日志的详细信息
type PodLogInfo struct {
	PodName      string
	Namespace    string
	Containers   []ContainerInfo
	SelectedLogs string
	Error        string
}

// KubectlLogs 等价于 kubectl logs <pod-name> [container-name] [-n namespace] [options]
func KubectlLogs(
	ctx context.Context,
	clientset kubernetes.Interface,
	namespace, podName string,
	opts LogOptions,
) (*PodLogInfo, error) {

	// 1. 验证Pod是否存在
	pod, err := clientset.CoreV1().Pods(namespace).Get(ctx, podName, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get pod %s/%s: %w", namespace, podName, err)
	}

	// 2. 获取容器信息
	containers := extractContainerInfo(pod)

	// 3. 确定要获取日志的容器
	targetContainer := determineTargetContainer(pod, opts.Container)
	if targetContainer == "" {
		return &PodLogInfo{
			PodName:    podName,
			Namespace:  namespace,
			Containers: containers,
			Error:      "no suitable container found",
		}, nil
	}

	// 4. 获取日志
	logContent, err := getContainerLogs(ctx, clientset, namespace, podName, targetContainer, opts)
	if err != nil {
		return &PodLogInfo{
			PodName:    podName,
			Namespace:  namespace,
			Containers: containers,
			Error:      err.Error(),
		}, nil
	}

	return &PodLogInfo{
		PodName:      podName,
		Namespace:    namespace,
		Containers:   containers,
		SelectedLogs: logContent,
	}, nil
}

// extractContainerInfo 从Pod中提取容器信息
func extractContainerInfo(pod *corev1.Pod) []ContainerInfo {
	var containers []ContainerInfo

	// 添加常规容器
	for _, container := range pod.Spec.Containers {
		state := "Waiting"
		ready := false

		// 查找容器状态
		for _, status := range pod.Status.ContainerStatuses {
			if status.Name == container.Name {
				ready = status.Ready
				if status.State.Running != nil {
					state = "Running"
				} else if status.State.Terminated != nil {
					state = fmt.Sprintf("Terminated(%d)", status.State.Terminated.ExitCode)
				} else if status.State.Waiting != nil {
					state = fmt.Sprintf("Waiting(%s)", status.State.Waiting.Reason)
				}
				break
			}
		}

		containers = append(containers, ContainerInfo{
			Name:  container.Name,
			Image: container.Image,
			State: state,
			Ready: ready,
		})
	}

	// 添加Init容器
	for _, initContainer := range pod.Spec.InitContainers {
		state := "Waiting"
		ready := false

		for _, status := range pod.Status.InitContainerStatuses {
			if status.Name == initContainer.Name {
				ready = status.Ready
				if status.State.Running != nil {
					state = "Running"
				} else if status.State.Terminated != nil {
					state = fmt.Sprintf("Terminated(%d)", status.State.Terminated.ExitCode)
				} else if status.State.Waiting != nil {
					state = fmt.Sprintf("Waiting(%s)", status.State.Waiting.Reason)
				}
				break
			}
		}

		containers = append(containers, ContainerInfo{
			Name:  initContainer.Name,
			Image: initContainer.Image,
			State: state,
			Ready: ready,
		})
	}

	return containers
}

// determineTargetContainer 确定要获取日志的目标容器
func determineTargetContainer(pod *corev1.Pod, specifiedContainer string) string {
	if specifiedContainer != "" {
		return specifiedContainer
	}

	// 如果只有一个容器，直接返回
	if len(pod.Spec.Containers) == 1 {
		return pod.Spec.Containers[0].Name
	}

	// 如果有多个容器但没有指定，返回空字符串让调用者选择
	if len(pod.Spec.Containers) > 1 {
		return ""
	}

	return ""
}

// getContainerLogs 获取指定容器的日志
func getContainerLogs(
	ctx context.Context,
	clientset kubernetes.Interface,
	namespace, podName, containerName string,
	opts LogOptions,
) (string, error) {

	logOptions := &corev1.PodLogOptions{
		Container: containerName,
		Follow:    false, // 暂时不支持follow模式
		Previous:  opts.Previous,
	}

	if opts.TailLines != nil {
		logOptions.TailLines = opts.TailLines
	}

	if opts.LimitBytes != nil {
		logOptions.LimitBytes = opts.LimitBytes
	}

	if opts.SinceTime != nil {
		sinceSeconds := int64(time.Since(*opts.SinceTime).Seconds())
		logOptions.SinceSeconds = &sinceSeconds
	}

	req := clientset.CoreV1().Pods(namespace).GetLogs(podName, logOptions)
	stream, err := req.Stream(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to open log stream: %w", err)
	}
	defer stream.Close()

	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, stream)
	if err != nil {
		return "", fmt.Errorf("failed to read logs: %w", err)
	}

	return buf.String(), nil
}

// ListPodsWithContainers 列出指定命名空间下的所有Pod及其容器
func ListPodsWithContainers(
	ctx context.Context,
	clientset kubernetes.Interface,
	namespace string,
) ([]PodLogInfo, error) {

	pods, err := clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list pods: %w", err)
	}

	var podInfos []PodLogInfo
	for _, pod := range pods.Items {
		containers := extractContainerInfo(&pod)
		podInfos = append(podInfos, PodLogInfo{
			PodName:    pod.Name,
			Namespace:  pod.Namespace,
			Containers: containers,
		})
	}

	return podInfos, nil
}

// GetRecentLogs 获取最近的N行日志
func GetRecentLogs(
	ctx context.Context,
	clientset kubernetes.Interface,
	namespace, podName string,
	tailLines int64,
) (string, error) {

	opts := LogOptions{
		TailLines: &tailLines,
	}

	info, err := KubectlLogs(ctx, clientset, namespace, podName, opts)
	if err != nil {
		return "", err
	}

	if info.Error != "" {
		return "", fmt.Errorf(info.Error)
	}

	return info.SelectedLogs, nil
}

// FormatLogOutput 格式化日志输出
func (pli *PodLogInfo) FormatLogOutput() string {
	var builder strings.Builder

	builder.WriteString(fmt.Sprintf("Pod: %s\n", pli.PodName))
	builder.WriteString(fmt.Sprintf("Namespace: %s\n", pli.Namespace))

	if len(pli.Containers) > 0 {
		builder.WriteString("\nContainers:\n")
		for _, container := range pli.Containers {
			builder.WriteString(fmt.Sprintf("  %s (%s): %s [Ready: %v]\n",
				container.Name, container.Image, container.State, container.Ready))
		}
	}

	if pli.SelectedLogs != "" {
		builder.WriteString("\n--- Logs ---\n")
		builder.WriteString(pli.SelectedLogs)
	}

	if pli.Error != "" {
		builder.WriteString(fmt.Sprintf("\nError: %s\n", pli.Error))
	}

	return builder.String()
}

// ValidateContainerExists 验证容器中是否存在指定容器
func ValidateContainerExists(
	ctx context.Context,
	clientset kubernetes.Interface,
	namespace, podName, containerName string,
) (bool, error) {

	pod, err := clientset.CoreV1().Pods(namespace).Get(ctx, podName, metav1.GetOptions{})
	if err != nil {
		return false, err
	}

	for _, container := range pod.Spec.Containers {
		if container.Name == containerName {
			return true, nil
		}
	}

	for _, initContainer := range pod.Spec.InitContainers {
		if initContainer.Name == containerName {
			return true, nil
		}
	}

	return false, nil
}
