package describe

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
)

// ResourceDescription 包含资源的详细描述信息
type ResourceDescription struct {
	Resource *unstructured.Unstructured
	Events   []corev1.Event
	Status   string
	Age      string
	Details  map[string]interface{}
}

// KubectlDescribe 等价于 kubectl describe <resource> <name> -n <namespace>
func KubectlDescribe(
	ctx context.Context,
	cli dynamic.Interface,
	clientset kubernetes.Interface,
	resourceType, namespace, name string,
) (*ResourceDescription, error) {

	// 1. 把常见别名硬编码成 GVR
	gvr, ok := alias2GVR[resourceType]
	if !ok {
		return nil, fmt.Errorf("unknown resource %q", resourceType)
	}

	// 2. 构造 ResourceInterface
	var resIf dynamic.ResourceInterface
	if namespace == "" {
		resIf = cli.Resource(gvr)
	} else {
		resIf = cli.Resource(gvr).Namespace(namespace)
	}

	// 3. 获取资源详情
	resource, err := resIf.Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}

	// 4. 获取相关事件
	events, err := getRelatedEvents(ctx, clientset, namespace, name, gvr)
	if err != nil {
		// 事件获取失败不影响主资源描述
		events = []corev1.Event{}
	}

	// 5. 计算状态和年龄
	status := extractStatus(resource)
	age := calculateAge(resource)

	// 6. 提取详细信息
	details := extractDetails(resource)

	return &ResourceDescription{
		Resource: resource,
		Events:   events,
		Status:   status,
		Age:      age,
		Details:  details,
	}, nil
}

// getRelatedEvents 获取与资源相关的事件
func getRelatedEvents(ctx context.Context, clientset kubernetes.Interface, namespace, name string, gvr schema.GroupVersionResource) ([]corev1.Event, error) {
	fieldSelectors := []string{
		fmt.Sprintf("involvedObject.name=%s", name),
		fmt.Sprintf("involvedObject.kind=%s", strings.Title(gvr.Resource[:len(gvr.Resource)-1])), // 去掉复数s
	}

	events, err := clientset.CoreV1().Events(namespace).List(ctx, metav1.ListOptions{
		FieldSelector: strings.Join(fieldSelectors, ","),
	})
	if err != nil {
		return nil, err
	}

	return events.Items, nil
}

// extractStatus 从资源中提取状态信息
func extractStatus(obj *unstructured.Unstructured) string {
	switch obj.GetKind() {
	case "Pod":
		if status, found, _ := unstructured.NestedMap(obj.Object, "status"); found {
			if phase, ok := status["phase"].(string); ok {
				return phase
			}
		}
	case "Deployment":
		if status, found, _ := unstructured.NestedMap(obj.Object, "status"); found {
			replicas := int64(0)
			readyReplicas := int64(0)

			if r, ok := status["replicas"].(int64); ok {
				replicas = r
			}
			if r, ok := status["readyReplicas"].(int64); ok {
				readyReplicas = r
			}

			return fmt.Sprintf("%d/%d replicas ready", readyReplicas, replicas)
		}
	case "Service":
		if spec, found, _ := unstructured.NestedMap(obj.Object, "spec"); found {
			if t, ok := spec["type"].(string); ok {
				clusterIP, _ := spec["clusterIP"].(string)
				return fmt.Sprintf("%s (%s)", t, clusterIP)
			}
		}
	}

	return "Unknown"
}

// calculateAge 计算资源创建时间到现在的时间差
func calculateAge(obj *unstructured.Unstructured) string {
	creationTime := obj.GetCreationTimestamp()
	if creationTime.IsZero() {
		return "Unknown"
	}

	duration := time.Since(creationTime.Time)

	if duration.Hours() >= 24 {
		return fmt.Sprintf("%dd", int(duration.Hours()/24))
	} else if duration.Hours() >= 1 {
		return fmt.Sprintf("%dh", int(duration.Hours()))
	} else if duration.Minutes() >= 1 {
		return fmt.Sprintf("%dm", int(duration.Minutes()))
	} else {
		return fmt.Sprintf("%ds", int(duration.Seconds()))
	}
}

// extractDetails 提取资源的详细信息用于描述
func extractDetails(obj *unstructured.Unstructured) map[string]interface{} {
	details := make(map[string]interface{})

	// 基本信息
	details["kind"] = obj.GetKind()
	details["apiVersion"] = obj.GetAPIVersion()
	details["name"] = obj.GetName()
	details["namespace"] = obj.GetNamespace()
	details["labels"] = obj.GetLabels()
	details["annotations"] = obj.GetAnnotations()
	details["creationTimestamp"] = obj.GetCreationTimestamp().Format(time.RFC3339)

	// 根据资源类型提取特定信息
	switch obj.GetKind() {
	case "Pod":
		if spec, found, _ := unstructured.NestedMap(obj.Object, "spec"); found {
			details["containers"] = extractContainers(spec)
			details["restartPolicy"] = spec["restartPolicy"]
			details["dnsPolicy"] = spec["dnsPolicy"]
		}

		if status, found, _ := unstructured.NestedMap(obj.Object, "status"); found {
			details["hostIP"] = status["hostIP"]
			details["podIP"] = status["podIP"]
			details["conditions"] = status["conditions"]
		}
	case "Deployment":
		if spec, found, _ := unstructured.NestedMap(obj.Object, "spec"); found {
			if template, found, _ := unstructured.NestedMap(spec, "template"); found {
				details["containers"] = extractContainers(template["spec"])
			}
			details["replicas"] = spec["replicas"]
			details["strategy"] = spec["strategy"]
		}
	case "Service":
		if spec, found, _ := unstructured.NestedMap(obj.Object, "spec"); found {
			details["type"] = spec["type"]
			details["clusterIP"] = spec["clusterIP"]
			details["ports"] = spec["ports"]
			details["selector"] = spec["selector"]
		}
	case "Node":
		if status, found, _ := unstructured.NestedMap(obj.Object, "status"); found {
			details["addresses"] = status["addresses"]
			details["capacity"] = status["capacity"]
			details["allocatable"] = status["allocatable"]
			details["conditions"] = status["conditions"]
		}
	}

	return details
}

// extractContainers 从spec中提取容器信息
func extractContainers(spec interface{}) []map[string]interface{} {
	specMap, ok := spec.(map[string]interface{})
	if !ok {
		return nil
	}

	containers, found, _ := unstructured.NestedSlice(specMap, "containers")
	if !found {
		return nil
	}

	result := make([]map[string]interface{}, 0, len(containers))
	for _, container := range containers {
		if c, ok := container.(map[string]interface{}); ok {
			containerInfo := map[string]interface{}{
				"name":  c["name"],
				"image": c["image"],
			}
			if ports, found := c["ports"]; found {
				containerInfo["ports"] = ports
			}
			if env, found := c["env"]; found {
				containerInfo["env"] = env
			}
			if resources, found := c["resources"]; found {
				containerInfo["resources"] = resources
			}
			result = append(result, containerInfo)
		}
	}

	return result
}

// FormatDescription 将描述信息格式化为易读的字符串
func (rd *ResourceDescription) FormatDescription() string {
	var builder strings.Builder

	// 头部信息
	builder.WriteString(fmt.Sprintf("Name:         %s\n", rd.Resource.GetName()))
	builder.WriteString(fmt.Sprintf("Namespace:    %s\n", rd.Resource.GetNamespace()))
	builder.WriteString(fmt.Sprintf("Labels:       %v\n", rd.Resource.GetLabels()))
	builder.WriteString(fmt.Sprintf("Annotations:  %v\n", rd.Resource.GetAnnotations()))
	builder.WriteString(fmt.Sprintf("API Version:  %s\n", rd.Resource.GetAPIVersion()))
	builder.WriteString(fmt.Sprintf("Kind:         %s\n", rd.Resource.GetKind()))
	builder.WriteString(fmt.Sprintf("Age:          %s\n", rd.Age))
	builder.WriteString(fmt.Sprintf("Status:       %s\n", rd.Status))

	// 详细信息
	builder.WriteString("\nSpec:\n")
	for key, value := range rd.Details {
		if key != "kind" && key != "apiVersion" && key != "name" && key != "namespace" &&
			key != "labels" && key != "annotations" && key != "creationTimestamp" {
			jsonBytes, _ := json.MarshalIndent(value, "  ", "  ")
			builder.WriteString(fmt.Sprintf("  %s: %s\n", key, string(jsonBytes)))
		}
	}

	// 事件信息
	if len(rd.Events) > 0 {
		builder.WriteString("\nEvents:\n")
		for _, event := range rd.Events {
			builder.WriteString(fmt.Sprintf("  Type:    %s\n", event.Type))
			builder.WriteString(fmt.Sprintf("  Reason:  %s\n", event.Reason))
			builder.WriteString(fmt.Sprintf("  Message: %s\n", event.Message))
			builder.WriteString(fmt.Sprintf("  From:    %s\n", event.Source.Component))
			builder.WriteString(fmt.Sprintf("  Age:     %s\n", calculateEventAge(event.LastTimestamp)))
			builder.WriteString("\n")
		}
	} else {
		builder.WriteString("\nEvents:       <none>\n")
	}

	return builder.String()
}

// calculateEventAge 计算事件的年龄
func calculateEventAge(timestamp metav1.Time) string {
	if timestamp.IsZero() {
		return "Unknown"
	}

	duration := time.Since(timestamp.Time)

	if duration.Hours() >= 24 {
		return fmt.Sprintf("%dd", int(duration.Hours()/24))
	} else if duration.Hours() >= 1 {
		return fmt.Sprintf("%dh", int(duration.Hours()))
	} else if duration.Minutes() >= 1 {
		return fmt.Sprintf("%dm", int(duration.Minutes()))
	} else {
		return fmt.Sprintf("%ds", int(duration.Seconds()))
	}
}

/* ---------- 私有：别名 -> GVR 表 ---------- */
var alias2GVR = map[string]schema.GroupVersionResource{
	"pods":         {Group: "", Version: "v1", Resource: "pods"},
	"po":           {Group: "", Version: "v1", Resource: "pods"},
	"services":     {Group: "", Version: "v1", Resource: "services"},
	"svc":          {Group: "", Version: "v1", Resource: "services"},
	"configmaps":   {Group: "", Version: "v1", Resource: "configmaps"},
	"cm":           {Group: "", Version: "v1", Resource: "configmaps"},
	"secrets":      {Group: "", Version: "v1", Resource: "secrets"},
	"deployments":  {Group: "apps", Version: "v1", Resource: "deployments"},
	"deploy":       {Group: "apps", Version: "v1", Resource: "deployments"},
	"nodes":        {Group: "", Version: "v1", Resource: "nodes"},
	"node":         {Group: "", Version: "v1", Resource: "nodes"},
	"namespaces":   {Group: "", Version: "v1", Resource: "namespaces"},
	"ns":           {Group: "", Version: "v1", Resource: "namespaces"},
	"replicasets":  {Group: "apps", Version: "v1", Resource: "replicasets"},
	"rs":           {Group: "apps", Version: "v1", Resource: "replicasets"},
	"daemonsets":   {Group: "apps", Version: "v1", Resource: "daemonsets"},
	"ds":           {Group: "apps", Version: "v1", Resource: "daemonsets"},
	"statefulsets": {Group: "apps", Version: "v1", Resource: "statefulsets"},
	"sts":          {Group: "apps", Version: "v1", Resource: "statefulsets"},
	"jobs":         {Group: "batch", Version: "v1", Resource: "jobs"},
	"cronjobs":     {Group: "batch", Version: "v1", Resource: "cronjobs"},
	"ingresses":    {Group: "networking.k8s.io", Version: "v1", Resource: "ingresses"},
	"ing":          {Group: "networking.k8s.io", Version: "v1", Resource: "ingresses"},
}
