# Kubernetes Go客户端连接问题解决方案

## 问题描述
Go程序无法连接到Kubernetes API服务器，报错"bad file descriptor"，但kubectl和curl都能正常工作。

## 解决方案

### 方案1：使用不同的Go版本
```bash
# 尝试使用不同的Go版本
go install golang.org/dl/go1.23.4@latest
go1.23.4 download
go1.23.4 run examples/get/get.go
```

### 方案2：强制使用系统DNS解析器
```bash
# 设置环境变量强制使用系统DNS
GODEBUG=netdns=cgo go run examples/get/get.go
```

### 方案3：修改client-go配置
在client.go中添加自定义的HTTP传输配置：

```go
func getKubeConfig() (*rest.Config, error) {
    // ... 现有代码 ...
    
    config, err = kubeConfig.ClientConfig()
    if err != nil {
        return nil, fmt.Errorf("failed to load out-of-cluster config: %w", err)
    }
    
    // 添加自定义传输配置
    config.Transport = &http.Transport{
        Proxy: http.ProxyFromEnvironment,
        DialContext: (&net.Dialer{
            Timeout:   30 * time.Second,
            KeepAlive: 30 * time.Second,
        }).DialContext,
        ForceAttemptHTTP2:     false, // 禁用HTTP/2
        MaxIdleConns:          100,
        IdleConnTimeout:       90 * time.Second,
        TLSHandshakeTimeout:   10 * time.Second,
        ExpectContinueTimeout: 1 * time.Second,
    }
    
    log.Println("Successfully configured client using out-of-cluster config")
    return config, nil
}
```

### 方案4：使用kubectl proxy
```bash
# 启动kubectl proxy
kubectl proxy --port=8080 &

# 修改程序连接到本地proxy
# 在代码中将API服务器地址改为 http://localhost:8080
```

### 方案5：检查系统网络配置
```bash
# 检查网络接口
ifconfig

# 检查路由表
netstat -rn

# 检查防火墙设置
sudo pfctl -s all

# 重置网络配置
sudo dscacheutil -flushcache
sudo killall -HUP mDNSResponder
```

### 方案6：使用Docker运行
```bash
# 创建Dockerfile
cat > Dockerfile << EOF
FROM golang:1.23-alpine
WORKDIR /app
COPY . .
RUN go mod download
CMD ["go", "run", "examples/get/get.go"]
EOF

# 构建并运行
docker build -t k8s-client .
docker run -v ~/.kube:/root/.kube k8s-client
```

## 推荐的调试步骤

1. 首先尝试方案2（强制使用系统DNS）
2. 如果不行，尝试方案3（修改传输配置）
3. 如果还不行，使用方案4（kubectl proxy）作为临时解决方案
4. 最后考虑方案1（更换Go版本）

## 长期解决方案

建议向Go社区报告这个问题，因为这可能是Go 1.24.3在macOS上的一个已知问题。
